import { Component, OnInit, ViewChild, ElementRef, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { HeaderComponent } from '../shared/ui/header/header.component';
import { CourseService, Course } from '../core/services/course.service';
import { EnrollmentService, Enrollment } from '../core/services/enrollment.service';
import { AuthService } from '../core/services/auth.service';
import { CourseCardComponent } from '../shared/ui/course-card/course-card.component';

@Component({
    selector: 'app-dashboard',
    standalone: true,
    imports: [CommonModule, RouterLink, HeaderComponent, CourseCardComponent],
    templateUrl: './dashboard.component.html',
    styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
    private courses = inject(CourseService);
    private enrollments = inject(EnrollmentService);
    private auth = inject(AuthService);

    @ViewChild('railLast', { static: false }) railLast?: ElementRef<HTMLDivElement>;
    @ViewChild('railNew', { static: false }) railNew?: ElementRef<HTMLDivElement>;

    enrolled: Enrollment[] = [];
    lastViewed: { course: Course; progressPercent: number }[] = [];
    newlyLaunched: Course[] = [];
    myGoalsCount = 3;

    ngOnInit() {
        const user = this.auth.currentUser;
        if (!user) return;

        this.enrollments.forUser(user.id).subscribe(enrs => {
            this.enrolled = enrs;
        });

        // Load newly launched courses (published only)
        this.courses.getNewlyLaunched(16).subscribe(cs => {
            this.newlyLaunched = cs;
            // For now, show the same courses in last viewed section
            // Remove complex last watched logic as requested
            this.lastViewed = cs.slice(0, 8).map(course => ({
                course,
                progressPercent: Math.floor(Math.random() * 100) // Temporary progress for display
            }));
        });
    }

    get enrolledCount() { return this.enrolled.length; }
    get certificatesCount() { return this.enrolled.filter(e => e.progressPercent >= 100).length; }

    /** Smooth horizontal scroll with buttons */
    scrollRail(ref: 'last' | 'new', dir: 1 | -1) {
        const rail = ref === 'last' ? this.railLast?.nativeElement : this.railNew?.nativeElement;
        if (!rail) return;
        const cardWidth = 296; // card + gap
        rail.scrollBy({ left: dir * cardWidth * 2, behavior: 'smooth' });
    }

    /** Convert vertical wheel to horizontal, prevent page scroll */
    onWheel(event: WheelEvent, ref: 'last' | 'new') {
        const rail = ref === 'last' ? this.railLast?.nativeElement : this.railNew?.nativeElement;
        if (!rail) return;
        // only when content actually overflows
        const canScroll = rail.scrollWidth > rail.clientWidth;
        if (!canScroll) return;
        event.preventDefault(); // stop page scroll
        rail.scrollBy({ left: (event.deltaY || event.deltaX), behavior: 'auto' });
    }
}
