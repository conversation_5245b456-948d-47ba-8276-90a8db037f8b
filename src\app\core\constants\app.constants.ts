/**
 * Application-wide constants
 * Centralizes all magic strings, URLs, and configuration values
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: 'http://localhost:3000',
  ENDPOINTS: {
    USERS: '/users',
    AUTH: '/auth',
    COURSES: '/courses',
    ENROLLMENTS: '/enrollments',
    PROGRESS: '/progress'
  }
} as const;

// Storage Keys
export const STORAGE_KEYS = {
  USER: 'lh_user',
  THEME: 'lh_theme',
  PREFERENCES: 'lh_preferences'
} as const;

// Default Values
export const DEFAULT_VALUES = {
  AVATAR_BASE_URL: 'https://i.pravatar.cc/150?u=',
  DEFAULT_ROLE: 'Learner',
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 10,
    MAX_PAGE_SIZE: 100
  }
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  AUTH: {
    INVALID_CREDENTIALS: 'Invalid credentials provided',
    EMAIL_ALREADY_EXISTS: 'Email already registered',
    USERNAME_ALREADY_EXISTS: 'Username already taken',
    REGISTRATION_FAILED: 'Registration failed. Please try again.',
    LOGIN_FAILED: 'Login failed. Please check your credentials.',
    UNAUTHORIZED: 'You are not authorized to access this resource',
    SESSION_EXPIRED: 'Your session has expired. Please log in again.'
  },
  NETWORK: {
    CONNECTION_ERROR: 'Network connection error. Please check your internet connection.',
    SERVER_ERROR: 'Server error occurred. Please try again later.',
    TIMEOUT: 'Request timeout. Please try again.'
  },
  VALIDATION: {
    REQUIRED_FIELD: 'This field is required',
    INVALID_EMAIL: 'Please enter a valid email address',
    INVALID_PASSWORD: 'Password must be at least 8 characters long',
    PASSWORDS_DONT_MATCH: 'Passwords do not match'
  }
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  AUTH: {
    REGISTRATION_SUCCESS: 'Registration successful! Welcome aboard!',
    LOGIN_SUCCESS: 'Login successful! Welcome back!',
    LOGOUT_SUCCESS: 'You have been logged out successfully'
  },
  COURSE: {
    ENROLLMENT_SUCCESS: 'Successfully enrolled in the course!',
    PROGRESS_SAVED: 'Your progress has been saved'
  }
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500
} as const;

// Regular Expressions
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  USERNAME: /^[a-zA-Z0-9_]{3,20}$/
} as const;

// Time Constants (in milliseconds)
export const TIME_CONSTANTS = {
  SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes
  TOKEN_REFRESH_INTERVAL: 5 * 60 * 1000, // 5 minutes
  DEBOUNCE_TIME: 300, // 300ms
  TOAST_DURATION: 3000 // 3 seconds
} as const;
